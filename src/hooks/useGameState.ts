import { useState, useEffect } from 'react';
import { GameEntry, PredictionLogicType, ExtensionFactor } from '@/types/game';
import { toast } from "sonner";
import { 
  calculateNextPosition, 
  getLossThreshold, 
  getWinThreshold, 
  getColumnLossCount, 
  getColumnWinCount,
  isPatternCell,
  getPatternSourceRow,
} from '@/utils/nextBetCalculation';
import { calculateNextPositionForWinLossPrevRows, determineWinLossPrevRowsResult } from '@/utils/winLossPrevRowsCalculation';
import { determineWinLossResult } from '@/utils/winLossCalculation';
import { createPredictionStrategy } from '@/utils/predictionStrategies';
import { calculateNextBet, determineResult, shouldResetDueToProfitThreshold, getLastResetType } from '@/utils/betCalculationService';
import { calculateNextPositionForExtendReset, calculateExtendResetNextBet } from '@/utils/extendResetPredictionCalculation';
import { calculateExtendNextBet, determineExtendResult } from '@/utils/extendPredictionCalculation';
import { calculateNextPositionForExtendPatternChange, getOppositeHandValue } from '@/utils/extendPatternChangePredictionCalculation';
import { calculateNextPositionForExtend211, calculateExtend211NextBet, determineExtend211Result } from '@/utils/extend211PredictionCalculation';
import { 
  calculateNextPositionForExtend211Recovery, 
  calculateExtend211RecoveryNextBet, 
  determineExtend211RecoveryResult,
  getConsecutiveLosses,
  getConsecutiveWins,
  checkIfInRecoveryMode
} from '@/utils/extend211RecoveryPredictionCalculation';

export const useGameState = () => {
  const [baseBet, setBaseBet] = useState<number>(1000);
  const [raiseBet, setRaiseBet] = useState<number>(1000);
  const [rowCount, setRowCount] = useState<number>(4);
  const [betStrategy, setBetStrategy] = useState<'loss' | 'win'>('loss');
  const [entries, setEntries] = useState<GameEntry[]>([]);
  const [predictionLogic, setPredictionLogic] = useState<PredictionLogicType>('same-no-recovery');
  const [resetBettingHands, setResetBettingHands] = useState<number>(5);
  const [lastResetIndex, setLastResetIndex] = useState<number>(-1);
  const [resetIndices, setResetIndices] = useState<number[]>([]);
  const [isGameReset, setIsGameReset] = useState<boolean>(false);
  const [currentResetColumn, setCurrentResetColumn] = useState<number | null>(null);
  const [extensionFactor, setExtensionFactor] = useState<ExtensionFactor>(2);

  useEffect(() => {
    if (entries.length === 0) {
      setIsGameReset(true);
    }
  }, [baseBet, raiseBet, entries.length]);

  const handleRowCountChange = (newRowCount: number) => {
    if (entries.length > 0) {
      toast.error("Cannot Change Grid Layout - Please reset the game before changing the grid layout.");
      return;
    }
    setRowCount(newRowCount);
  };

  const handleExtensionFactorChange = (newExtensionFactor: ExtensionFactor) => {
    if (entries.length > 0) {
      toast.error("Cannot Change Extension Factor - Please reset the game before changing the extension factor.");
      return;
    }
    setExtensionFactor(newExtensionFactor);
  };

  const handleHandSelectForPosition = async (columnNumber: number | null, rowNumber: number | null, value: 'P' | 'B') => {
    if (columnNumber !== null && rowNumber !== null && rowNumber === 1) {
      const existingEntry = entries.find(
        entry => entry.columnNumber === columnNumber && entry.rowNumber === rowNumber
      );
      
      if (existingEntry) {
        const updatedEntries = entries.map(entry => 
          entry.id === existingEntry.id ? { ...entry, handValue: value } : entry
        );
        
        const entriesInColumn = updatedEntries.filter(
          entry => entry.columnNumber === columnNumber && entry.rowNumber > 1
        );
        
        const updateEntriesWithResultsPromises = updatedEntries.map(async entry => {
          if (entry.columnNumber === columnNumber && entry.rowNumber > 1) {
            const result = await determineResult(
              entry.handValue!, 
              entry.columnNumber, 
              entry.rowNumber, 
              updatedEntries, 
              predictionLogic, 
              null
            );
            return { ...entry, result };
          }
          return entry;
        });
        
        Promise.all(updateEntriesWithResultsPromises)
          .then(updatedEntriesWithResults => {
            let cumulativeProfit = 0;
            const finalUpdatedEntries = updatedEntriesWithResults.map((entry, index) => {
              if (index === 0) {
                cumulativeProfit = entry.result === 'W' ? 
                  entry.betAmount : (entry.result === 'L' ? -entry.betAmount : 0);
                return { ...entry, cumulativeProfitLoss: cumulativeProfit };
              }
              
              if (entry.result === 'W') {
                cumulativeProfit += entry.betAmount;
              } else if (entry.result === 'L') {
                cumulativeProfit -= entry.betAmount;
              }
              
              return { ...entry, cumulativeProfitLoss: cumulativeProfit };
            });
            
            setEntries(finalUpdatedEntries);
          });
        
        return;
      } else {
        let handValueToUse = value;
        let isTransitionEntry = false;
        const isPatternCellEntry = isPatternCell(rowNumber, predictionLogic, extensionFactor, rowCount);
        
        if ((predictionLogic === 'extend' || predictionLogic === 'extend-reset') && columnNumber > 1) {
          console.log(`Checking for column transition to Column ${columnNumber}, Row 1`);
          const prevColumnEntries = entries.filter(
            entry => entry.columnNumber === columnNumber - 1
          );
          
          const row5Entry = prevColumnEntries.find(entry => entry.rowNumber === 5);
          
          if (row5Entry && row5Entry.handValue) {
            handValueToUse = row5Entry.handValue;
            isTransitionEntry = true;
          }
        }
        else if (predictionLogic === 'same-no-recovery' && columnNumber > 1) {
          console.log(`Checking for column transition to Column ${columnNumber}, Row 1`);
          const prevColumnEntries = entries.filter(
            entry => entry.columnNumber === columnNumber - 1
          );
          
          if (prevColumnEntries.length > 0) {
            prevColumnEntries.sort((a, b) => b.rowNumber - a.rowNumber);
            const lastRowEntry = prevColumnEntries[0];
            
            if (lastRowEntry) {
              handValueToUse = lastRowEntry.handValue!;
              isTransitionEntry = true;
            }
          }
        }
        
        const newEntry: GameEntry = {
          id: entries.length + 1,
          handValue: handValueToUse,
          columnNumber: columnNumber,
          rowNumber: 1,
          betAmount: 0,
          result: 'N',
          cumulativeProfitLoss: entries.length > 0 ? entries[entries.length - 1].cumulativeProfitLoss : 0,
          isPatternCell: isPatternCellEntry
        };
        
        const updatedEntries = [...entries, newEntry];
        setEntries(updatedEntries);
        
        if (isTransitionEntry) {
          toast.info(`Column Transition - First hand of Column ${columnNumber} populated from Row 5 of Column ${columnNumber-1}`);
        } else if (isPatternCellEntry) {
          toast.info(`Pattern Cell - Column ${columnNumber}, Row 1 is set as a pattern cell with value from Column 1, Row 5`);
        }
        
        return;
      }
    }
    
    await handleHandSelect(value);
  };

  const handleHandSelect = async (value: 'P' | 'B') => {
    let nextPosition;
    
    if (predictionLogic === 'win-loss-prev-rows') {
      nextPosition = calculateNextPositionForWinLossPrevRows(entries, rowCount);
    } else if (predictionLogic === 'extend-reset') {
      nextPosition = calculateNextPositionForExtendReset(entries, rowCount, extensionFactor);
      console.log(`handleHandSelect (extend-reset) - Next position calculated: Column ${nextPosition.columnNumber}, Row ${nextPosition.rowNumber}`);
    } else if (predictionLogic === 'extend-pattern-change') {
      nextPosition = calculateNextPositionForExtendPatternChange(entries, rowCount, extensionFactor);
      console.log(`handleHandSelect (extend-pattern-change) - Next position calculated: Column ${nextPosition.columnNumber}, Row ${nextPosition.rowNumber}`);
    } else if (predictionLogic === 'extend-2-1-1') {
      nextPosition = calculateNextPositionForExtend211(entries, rowCount, extensionFactor);
      console.log(`handleHandSelect (extend-2-1-1) - Next position calculated: Column ${nextPosition.columnNumber}, Row ${nextPosition.rowNumber}`);
    } else if (predictionLogic === 'extend-2-1-1-recovery') {
      nextPosition = calculateNextPositionForExtend211Recovery(entries, rowCount, extensionFactor);
      console.log(`handleHandSelect (extend-2-1-1-recovery) - Next position calculated: Column ${nextPosition.columnNumber}, Row ${nextPosition.rowNumber}`);
    } else {
      nextPosition = calculateNextPosition(entries, rowCount, predictionLogic, extensionFactor);
      
      if (predictionLogic === 'extend') {
        console.log(`handleHandSelect (extend) - Next position calculated: Column ${nextPosition.columnNumber}, Row ${nextPosition.rowNumber}`);
      }
    }
    
    const strategy = createPredictionStrategy(predictionLogic);
    
    let shouldReset = false;
    let isProfitReset = false;
    let lastResetColumn = null;
    let lastResetType = null;
    
    if (entries.length > 0 && predictionLogic === 'extend-reset') {
      const resetEntry = [...entries].reverse().find(entry => entry.resetPoint === true);
      if (resetEntry) {
        lastResetColumn = resetEntry.columnNumber;
        lastResetType = resetEntry.resetType;
      }
      
      console.log(`Last reset info - Column: ${lastResetColumn}, Type: ${lastResetType || 'none'}`);
      
      const lastEntry = entries[entries.length - 1];
      if (!lastEntry.resetPoint) {
        isProfitReset = shouldResetDueToProfitThreshold(entries, baseBet);
        console.log(`Checking reset conditions in handleHandSelect - Profit: ${isProfitReset}`);
        shouldReset = isProfitReset;
        
        if (shouldReset && entries.length > 0) {
          const updatedEntries = [...entries];
          const lastEntryIndex = updatedEntries.length - 1;
          
          updatedEntries[lastEntryIndex] = {
            ...updatedEntries[lastEntryIndex],
            resetPoint: true,
            resetType: 'profit'
          };
          
          setEntries(updatedEntries);
          
          lastResetColumn = updatedEntries[lastEntryIndex].columnNumber;
          lastResetType = updatedEntries[lastEntryIndex].resetType;
          
          if (isProfitReset) {
            let lastPrevResetIndex = -1;
            for (let i = lastEntryIndex - 1; i >= 0; i--) {
              if (updatedEntries[i].resetPoint === true) {
                lastPrevResetIndex = i;
                break;
              }
            }
            
            const startProfit = lastPrevResetIndex >= 0 ? updatedEntries[lastPrevResetIndex].cumulativeProfitLoss : 0;
            const currentProfit = updatedEntries[lastEntryIndex].cumulativeProfitLoss - startProfit;
            
            toast.info(`Profit threshold reached (${currentProfit} >= ${baseBet * 5}): Bet reset to ${baseBet}`);
          }
        }
      }
    }
    
    let nextBet;
    
    if (predictionLogic === 'extend') {
      console.log('Using calculateExtendNextBet for next bet calculation');
      nextBet = calculateExtendNextBet(entries, isGameReset, baseBet, raiseBet, rowCount, extensionFactor);
      console.log(`Next bet calculated for extend: ${nextBet}`);
    } else if (predictionLogic === 'extend-reset') {
      console.log('Using calculateExtendResetNextBet for next bet calculation');
      nextBet = calculateExtendResetNextBet(
        entries, 
        isGameReset, 
        baseBet, 
        raiseBet, 
        rowCount, 
        extensionFactor, 
        shouldReset, 
        isProfitReset ? 'profit' : null,
        lastResetColumn
      );
      console.log(`Next bet calculated for extend-reset: ${nextBet}`);
      console.log(`Reset information - shouldReset: ${shouldReset}, resetType: ${isProfitReset ? 'profit' : 'none'}`);
    } else if (predictionLogic === 'extend-pattern-change') {
      console.log('Using strategy.calculateBet for extend-pattern-change next bet calculation');
      nextBet = strategy.calculateBet({
        entries,
        isGameReset,
        baseBet,
        raiseBet,
        rowCount,
        extensionFactor
      });
      console.log(`Next bet calculated for extend-pattern-change: ${nextBet}`);
    } else if (predictionLogic === 'extend-2-1-1') {
      console.log('Using calculateExtend211NextBet for next bet calculation');
      nextBet = calculateExtend211NextBet(entries, isGameReset, baseBet, raiseBet, rowCount, extensionFactor);
      console.log(`Next bet calculated for extend-2-1-1: ${nextBet}`);
    } else if (predictionLogic === 'extend-2-1-1-recovery') {
      nextBet = calculateExtend211RecoveryNextBet(entries, isGameReset, baseBet, raiseBet, rowCount, extensionFactor);
      console.log(`Next bet calculated for extend-2-1-1-recovery: ${nextBet}`);
    } else {
      nextBet = calculateNextBet({
        entries,
        isGameReset,
        baseBet,
        raiseBet,
        rowCount,
        lastResetIndex,
        resetIndices,
        recoveryModeActive: false,
        recoveryModeBetAmount: 0,
        currentResetColumn,
        mirrorPrediction: null,
        betStrategy,
        resetBettingHands,
        predictionLogic,
        extensionFactor
      });
    }
    
    // Ensure nextBet is a resolved number
    if (typeof nextBet === 'object' && nextBet !== null) {
      nextBet = await nextBet;
    }
    
    if (typeof nextBet !== 'number') {
      throw new Error('Next bet is not a number');
    }
    
    let maxColumns = rowCount === 3 ? 28 : rowCount === 4 ? 21 : rowCount === 6 ? 14 : 10;
    
    if (predictionLogic === 'extend' || predictionLogic === 'extend-reset' || predictionLogic === 'extend-pattern-change' || predictionLogic === 'extend-2-1-1' || predictionLogic === 'extend-2-1-1-recovery') {
      maxColumns = 21;
    }
    
    if (nextPosition.columnNumber > maxColumns) {
      toast.error("Game Complete - Maximum number of hands reached");
      return;
    }

    let willCompleteColumn = false;
    
    if (predictionLogic === 'same-no-recovery' || predictionLogic === 'same-recovery') {
      willCompleteColumn = nextPosition.rowNumber === rowCount;
    } else if (predictionLogic === 'extend-2-1-1' || predictionLogic === 'extend-2-1-1-recovery') {
      // For extend-2-1-1, column progression happens after Row 7
      willCompleteColumn = nextPosition.rowNumber === 7 && !isPatternCell(nextPosition.rowNumber, predictionLogic, extensionFactor, rowCount);
    }
    
    if ((predictionLogic === 'same-no-recovery' || predictionLogic === 'same-recovery') && willCompleteColumn) {
      console.log(`Last row in column ${nextPosition.columnNumber} detected - preparing for column transition`);
    }
    
    const isAddingPatternCell = isPatternCell(nextPosition.rowNumber, predictionLogic, extensionFactor, rowCount);
      
    if (isAddingPatternCell) {
      console.log(`Adding pattern cell at column ${nextPosition.columnNumber}, row ${nextPosition.rowNumber}`);
      
      if (predictionLogic === 'extend-pattern-change' && nextPosition.rowNumber === 4) {
        const row3Entry = entries.find(
          entry => entry.columnNumber === nextPosition.columnNumber && entry.rowNumber === 3
        );
        
        if (row3Entry?.handValue) {
          // Use the OPPOSITE value from Row 3
          value = getOppositeHandValue(row3Entry.handValue);
          console.log(`Using OPPOSITE value ${value} from Row 3 value ${row3Entry.handValue}`);
        } else {
          console.log(`No Row 3 value found to determine opposite for Row 4`);
        }
      } else if (isAddingPatternCell) {
        const sourceRow = getPatternSourceRow(nextPosition.rowNumber, rowCount);
        
        const sourceEntry = entries.find(
          entry => entry.columnNumber === nextPosition.columnNumber && 
                   entry.rowNumber === sourceRow &&
                   !entry.isPatternCell
        );
        
        if (sourceEntry?.handValue) {
          console.log(`Using source value ${sourceEntry.handValue} from row ${sourceRow}`);
          value = sourceEntry.handValue;
        }
      }
    }
    
    let result: 'W' | 'L' | 'N';
    
    if (predictionLogic === 'win-loss') {
      result = determineWinLossResult(value);
    } else if (predictionLogic === 'win-loss-prev-rows') {
      result = determineWinLossPrevRowsResult(value);
    } else if (predictionLogic === 'extend') {
      result = determineExtendResult(value, nextPosition.columnNumber, nextPosition.rowNumber, entries);
      console.log(`Determined result for extend: ${result}`);
    } else if (predictionLogic === 'extend-pattern-change') {
      result = strategy.determineResult(value, nextPosition.columnNumber, nextPosition.rowNumber, entries);
      console.log(`Determined result for extend-pattern-change: ${result}`);
    } else if (predictionLogic === 'extend-2-1-1') {
      result = determineExtend211Result(value, nextPosition.columnNumber, nextPosition.rowNumber, entries);
      console.log(`Determined result for extend-2-1-1: ${result}`);
    } else if (predictionLogic === 'extend-2-1-1-recovery') {
      result = determineExtend211RecoveryResult(value, nextPosition.columnNumber, nextPosition.rowNumber, entries);
      console.log(`Determined result for extend-2-1-1-recovery: ${result}`);
    } else {
      result = await determineResult(
        value, 
        nextPosition.columnNumber, 
        nextPosition.rowNumber, 
        entries, 
        predictionLogic,
        null
      );
    }

    const previousProfitLoss = entries.length > 0 
      ? entries[entries.length - 1].cumulativeProfitLoss 
      : 0;

    let profitLoss = previousProfitLoss;
    if (result === 'W') {
      profitLoss += nextBet;
    } else if (result === 'L') {
      profitLoss -= nextBet;
    }

    const betAmount = nextBet;
    
    const newEntry: GameEntry = {
      id: entries.length + 1,
      handValue: value,
      columnNumber: nextPosition.columnNumber,
      rowNumber: nextPosition.rowNumber,
      betAmount: isAddingPatternCell ? 0 : betAmount,
      result: isAddingPatternCell ? 'N' : result,
      cumulativeProfitLoss: profitLoss,
      isPatternCell: isAddingPatternCell,
      resetPoint: false
    };

    if (predictionLogic === 'extend-2-1-1-recovery') {
      const consecutiveLosses = getConsecutiveLosses(entries);
      const consecutiveWins = getConsecutiveWins(entries);
      const isInRecoveryMode = checkIfInRecoveryMode(entries);

      // Start recovery mode after 3 consecutive losses
      if (consecutiveLosses === 3 && !isInRecoveryMode) {
        newEntry.recoveryModeStart = true;
        console.log(`Setting recoveryModeStart flag for entry ${newEntry.id}`);
        toast.info("Recovery mode activated after 3 consecutive losses");
      }

      // End recovery mode after 2 consecutive wins
      if (isInRecoveryMode && consecutiveWins >= 2 && result === 'W') {
        newEntry.recoveryModeEnd = true;
        console.log(`Setting recoveryModeEnd flag for entry ${newEntry.id}`);
        toast.success("Recovery mode ended after 2 consecutive wins");
      }
    }

    if (predictionLogic === 'extend' || predictionLogic === 'extend-reset' || predictionLogic === 'extend-pattern-change' || predictionLogic === 'extend-2-1-1' || predictionLogic === 'extend-2-1-1-recovery') {
      console.log(`Creating new entry:`, {
        id: newEntry.id,
        column: newEntry.columnNumber,
        row: newEntry.rowNumber,
        handValue: newEntry.handValue,
        betAmount: newEntry.betAmount,
        result: newEntry.result,
        isPatternCell: newEntry.isPatternCell,
        resetPoint: newEntry.resetPoint
      });
    }

    let updatedEntries = [...entries, newEntry];
    
    // Remove extend-2-1-1 from this condition since we'll handle it separately
    if ((predictionLogic === 'same-no-recovery' || predictionLogic === 'same-recovery') && willCompleteColumn) {
      console.log(`Auto-adding Row 1 of Column ${nextPosition.columnNumber + 1} with value ${value}`);
      
      const nextColumnRow1: GameEntry = {
        id: updatedEntries.length + 1,
        handValue: value,
        columnNumber: nextPosition.columnNumber + 1,
        rowNumber: 1,
        betAmount: 0,
        result: 'N',
        cumulativeProfitLoss: profitLoss,
        isPatternCell: false,
        resetPoint: false
      };
      
      updatedEntries = [...updatedEntries, nextColumnRow1];
      
      toast.info(`Column Transition - First hand of Column ${nextPosition.columnNumber + 1} automatically added`);
    }
    
    // For extend-2-1-1, we need to check if we're at Row 7 and it's the last row in the column
    if (predictionLogic === 'extend-2-1-1' && nextPosition.rowNumber === 7) {
      // After adding Row 7, we need to auto-populate Row 1 of the next column
      console.log(`For extend-2-1-1: Auto-adding Row 1 of Column ${nextPosition.columnNumber + 1} with value ${value}`);
      
      const nextColumnRow1: GameEntry = {
        id: updatedEntries.length + 1,
        handValue: value, // Use Row 7's value, not Row 6's
        columnNumber: nextPosition.columnNumber + 1,
        rowNumber: 1,
        betAmount: 0,
        result: 'N',
        cumulativeProfitLoss: profitLoss,
        isPatternCell: true, // Row 1 is a pattern cell
        resetPoint: false
      };
      
      updatedEntries = [...updatedEntries, nextColumnRow1];
      
      toast.info(`Column Transition - First hand of Column ${nextPosition.columnNumber + 1} automatically added`);
    }
    
    // For extend-2-1-1-recovery, we need to check if we're at Row 7 and it's the last row in the column
    if (predictionLogic === 'extend-2-1-1-recovery' && nextPosition.rowNumber === 7) {
      console.log(`For extend-2-1-1-recovery: Auto-adding Row 1 of Column ${nextPosition.columnNumber + 1} with value ${value}`);
      
      const nextColumnRow1: GameEntry = {
        id: updatedEntries.length + 1,
        handValue: value, // Use Row 7's value, not Row 6's
        columnNumber: nextPosition.columnNumber + 1,
        rowNumber: 1,
        betAmount: 0,
        result: 'N',
        cumulativeProfitLoss: profitLoss,
        isPatternCell: true, // Row 1 is a pattern cell
        resetPoint: false
      };
      
      updatedEntries = [...updatedEntries, nextColumnRow1];
      
      toast.info(`Column Transition - First hand of Column ${nextPosition.columnNumber + 1} automatically added`);
    }
    
    // For extend-2-1-1, we need to check if we're at Row 5 and auto-populate Row 6 as a pattern cell
    if (predictionLogic === 'extend-2-1-1' && nextPosition.rowNumber === 5 && !isAddingPatternCell) {
      console.log(`For extend-2-1-1: Auto-populating pattern cell at column ${nextPosition.columnNumber}, row 6 with value ${value}`);
      
      const row6Entry: GameEntry = {
        id: updatedEntries.length + 1,
        handValue: value,
        columnNumber: nextPosition.columnNumber,
        rowNumber: 6,
        betAmount: 0,
        result: 'N',
        cumulativeProfitLoss: profitLoss,
        isPatternCell: true,
        resetPoint: false
      };
      
      updatedEntries = [...updatedEntries, row6Entry];
    }
    
    // For extend-2-1-1-recovery, we need to check if we're at Row 5 and auto-populate Row 6 as a pattern cell
    if (predictionLogic === 'extend-2-1-1-recovery' && nextPosition.rowNumber === 5 && !isAddingPatternCell) {
      console.log(`For extend-2-1-1-recovery: Auto-populating pattern cell at column ${nextPosition.columnNumber}, row 6 with value ${value}`);
      
      const row6Entry: GameEntry = {
        id: updatedEntries.length + 1,
        handValue: value,
        columnNumber: nextPosition.columnNumber,
        rowNumber: 6,
        betAmount: 0,
        result: 'N',
        cumulativeProfitLoss: profitLoss,
        isPatternCell: true,
        resetPoint: false
      };
      
      updatedEntries = [...updatedEntries, row6Entry];
    }
    
    setEntries(updatedEntries);

    if (predictionLogic === 'same-no-recovery' && betStrategy === 'loss') {
      const bettingEntries = updatedEntries.filter(entry => entry.result !== 'N');
      
      const bettingHandsSinceReset = bettingEntries.length - (lastResetIndex + 1);
      
      const currentProfit = updatedEntries.length > 0 ? updatedEntries[updatedEntries.length - 1].cumulativeProfitLoss : 0;
      
      const profitSinceReset = currentProfit - (lastResetIndex >= 0 && lastResetIndex < bettingEntries.length ? 
                              bettingEntries[lastResetIndex].cumulativeProfitLoss : 0);
      
      if (bettingHandsSinceReset >= resetBettingHands && profitSinceReset >= (resetBettingHands * baseBet)) {
        setLastResetIndex(bettingEntries.length - 1);
        setResetIndices(prev => [...prev, bettingEntries[bettingEntries.length - 1].id]);
        setCurrentResetColumn(nextPosition.columnNumber);
      }
    }

    if (result === 'N' && predictionLogic !== 'win-loss') {
      toast.info("First Hand in Column - This is a no-bet hand. Observing the outcome.");
    } else if (predictionLogic === 'win-loss') {
      if (result === 'W') {
        toast.success(`Win result added - Next bet will be calculated based on win-loss logic`);
      } else if (result === 'L') {
        toast.error(`Loss result added - Next bet will be calculated based on win-loss logic`);
      }
    } else if (getColumnLossCount(entries, nextPosition.columnNumber - 1) >= getLossThreshold(rowCount) && nextPosition.rowNumber === 2) {
      const previousColumnWins = getColumnWinCount(entries, nextPosition.columnNumber - 1);
      if (previousColumnWins >= getWinThreshold(rowCount)) {
        toast.info(`Base Bet Unchanged - Previous column had ${previousColumnWins} wins. Base bet remains at ${baseBet}`);
      } else {
        toast.info(`Base Bet Increased - Previous column had ${getColumnLossCount(entries, nextPosition.columnNumber - 1)} losses. Base bet increased.`);
      }
    }
  };

  const handleUndo = () => {
    if (entries.length === 0) return;
    
    const lastEntry = entries[entries.length - 1];
    
    // Handle column transition undo for Same-No-Recovery and Same-Recovery
    if ((predictionLogic === 'same-no-recovery' || predictionLogic === 'same-recovery') && entries.length >= 2) {
      const secondLastEntry = entries[entries.length - 2];
      
      // If we're undoing Row 1 of any column, we should also undo the last row of the previous column
      if (lastEntry.rowNumber === 1 && lastEntry.columnNumber > 1) {
        console.log(`Undoing Row 1 in Column ${lastEntry.columnNumber}`);
        
        // Find the last row in the previous column
        const prevColumnEntries = entries.filter(
          entry => entry.columnNumber === lastEntry.columnNumber - 1
        );
        
        if (prevColumnEntries.length > 0) {
          // Sort by row number in descending order to find the last row
          prevColumnEntries.sort((a, b) => b.rowNumber - a.rowNumber);
          const lastRowInPrevColumn = prevColumnEntries[0];
          
          console.log(`Also removing last row (Row ${lastRowInPrevColumn.rowNumber}) from Column ${lastEntry.columnNumber - 1}`);
          
          const entriesToRemove = [lastEntry.id, lastRowInPrevColumn.id];
          const newEntries = entries.filter(entry => !entriesToRemove.includes(entry.id));
          
          setEntries(newEntries);
          
          toast.success(`Undo successful - Removed Row 1 of Column ${lastEntry.columnNumber} and Row ${lastRowInPrevColumn.rowNumber} of Column ${lastEntry.columnNumber - 1}`);
          return;
        }
      }
      
      // Original column transition check for Same-No-Recovery and Same-Recovery
      if (lastEntry.rowNumber === 1 && 
          secondLastEntry.rowNumber === rowCount && 
          lastEntry.columnNumber === secondLastEntry.columnNumber + 1) {
        
        console.log(`Undoing column transition from Column ${secondLastEntry.columnNumber} to Column ${lastEntry.columnNumber}`);
        
        const entriesToRemove = [lastEntry.id, secondLastEntry.id];
        const newEntries = entries.filter(entry => !entriesToRemove.includes(entry.id));
        
        setEntries(newEntries);
        
        toast.success(`Undo successful - Removed column transition from Column ${secondLastEntry.columnNumber} to Column ${lastEntry.columnNumber}`);
        return;
      }
    }
    
    // Special handling for recovery mode flags
    if (predictionLogic === 'extend-2-1-1-recovery') {
      // If undoing an entry with recoveryModeStart flag, we need to reset recovery mode
      if (lastEntry.recoveryModeStart) {
        console.log('Undoing recovery mode activation');
        toast.info('Undoing recovery mode activation');
      }
      
      // If undoing an entry with recoveryModeEnd flag, we need to restore recovery mode
      if (lastEntry.recoveryModeEnd) {
        console.log('Undoing recovery mode deactivation');
        toast.info('Undoing recovery mode deactivation');
      }
    }
    
    // Updated undo logic for extend-pattern-change to match extend logic
    if (predictionLogic === 'extend' || predictionLogic === 'extend-reset' || predictionLogic === 'extend-pattern-change' || predictionLogic === 'extend-2-1-1' || predictionLogic === 'extend-2-1-1-recovery') {
      console.log(`Undoing entry at Column ${lastEntry.columnNumber}, Row ${lastEntry.rowNumber}`);
      
      // Special handling for Extend-2-1-1 logic and Extend-2-1-1-Recovery logic
      if (predictionLogic === 'extend-2-1-1' || predictionLogic === 'extend-2-1-1-recovery') {
        // Case 1: Undo for Row 7 should also undo Row 6
        if (lastEntry.rowNumber === 7) {
          console.log(`Extend-2-1-1 special case: Undoing Row 7 in Column ${lastEntry.columnNumber}`);
          
          // Find Row 6 entry as it's dependent on Row 7
          const row6Entry = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber === 6
          );
          
          const entriesToRemove = [lastEntry.id];
          
          if (row6Entry) {
            console.log(`Also removing Row 6 from Column ${lastEntry.columnNumber}`);
            entriesToRemove.push(row6Entry.id);
          }
          
          // Also remove any entries in the next column that might have been auto-populated
          const nextColumnEntries = entries.filter(
            entry => entry.columnNumber === lastEntry.columnNumber + 1
          ).map(entry => entry.id);
          
          if (nextColumnEntries.length > 0) {
            console.log(`Also removing dependent entries from Column ${lastEntry.columnNumber + 1}`);
            entriesToRemove.push(...nextColumnEntries);
          }
          
          const newEntries = entries.filter(entry => !entriesToRemove.includes(entry.id));
          setEntries(newEntries);
          
          toast.success(`Undo successful - Removed Row 7 and dependent entries`);
          return;
        }
        
        // NEW CASE: Undo for Row 6 should also undo Row 5
        if (lastEntry.rowNumber === 6) {
          console.log(`Extend-2-1-1 special case: Undoing Row 6 in Column ${lastEntry.columnNumber}`);
          
          // Find Row 5 entry as Row 6 is auto-populated from Row 5
          const row5Entry = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber === 5
          );
          
          const entriesToRemove = [lastEntry.id];
          
          if (row5Entry) {
            console.log(`Also removing Row 5 from Column ${lastEntry.columnNumber}`);
            entriesToRemove.push(row5Entry.id);
            
            // Also remove Row 4 since Row 5 depends on Row 4
            const row4Entry = entries.find(
              entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber === 4
            );
            
            if (row4Entry) {
              console.log(`Also removing Row 4 from Column ${lastEntry.columnNumber}`);
              entriesToRemove.push(row4Entry.id);
            }
          }
          
          // Also remove Row 7 if it exists
          const row7Entry = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber === 7
          );
          
          if (row7Entry) {
            console.log(`Also removing Row 7 from Column ${lastEntry.columnNumber}`);
            entriesToRemove.push(row7Entry.id);
            
            // Also remove any entries in the next column that might have been auto-populated
            const nextColumnEntries = entries.filter(
              entry => entry.columnNumber === lastEntry.columnNumber + 1
            ).map(entry => entry.id);
            
            if (nextColumnEntries.length > 0) {
              console.log(`Also removing dependent entries from Column ${lastEntry.columnNumber + 1}`);
              entriesToRemove.push(...nextColumnEntries);
            }
          }
          
          const newEntries = entries.filter(entry => !entriesToRemove.includes(entry.id));
          setEntries(newEntries);
          
          toast.success(`Undo successful - Removed Row 6 and dependent entries`);
          return;
        }
        
        // Case 2: Undo for Row 1 should undo Row 7 from previous column (not Row 5)
        if (lastEntry.rowNumber === 1 && lastEntry.columnNumber > 1) {
          console.log(`Extend-2-1-1 special case: Undoing Row 1 in Column ${lastEntry.columnNumber}`);
          
          // Find Row 7 entry from previous column
          const prevColRow7Entry = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber - 1 && entry.rowNumber === 7
          );
          
          const entriesToRemove = [lastEntry.id];
          
          if (prevColRow7Entry) {
            console.log(`Also removing Row 7 from Column ${lastEntry.columnNumber - 1}`);
            entriesToRemove.push(prevColRow7Entry.id);
          }
          
          // Also remove any dependent rows in the current column
          const dependentRows = entries.filter(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber > 1
          ).map(entry => entry.id);
          
          entriesToRemove.push(...dependentRows);
          
          const newEntries = entries.filter(entry => !entriesToRemove.includes(entry.id));
          setEntries(newEntries);
          
          toast.success(`Undo successful - Removed Column ${lastEntry.columnNumber} Row 1 and related entries`);
          return;
        }
        
        // Case 3: Undo for Row 5 should undo Row 4
        if (lastEntry.rowNumber === 5) {
          console.log(`Extend-2-1-1 special case: Undoing Row 5 in Column ${lastEntry.columnNumber}`);
          
          // Find Row 4 entry as Row 5 is dependent on Row 4
          const row4Entry = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber === 4
          );
          
          const entriesToRemove = [lastEntry.id];
          
          if (row4Entry) {
            console.log(`Also removing Row 4 from Column ${lastEntry.columnNumber}`);
            entriesToRemove.push(row4Entry.id);
            
            // Also remove any dependent rows (Row 6 and Row 7)
            const dependentRows = entries.filter(
              entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber > 5
            ).map(entry => entry.id);
            
            entriesToRemove.push(...dependentRows);
            
            // Also remove any entries in the next column that might have been auto-populated
            const nextColumnEntries = entries.filter(
              entry => entry.columnNumber === lastEntry.columnNumber + 1
            ).map(entry => entry.id);
            
            if (nextColumnEntries.length > 0) {
              console.log(`Also removing dependent entries from Column ${lastEntry.columnNumber + 1}`);
              entriesToRemove.push(...nextColumnEntries);
            }
          }
          
          const newEntries = entries.filter(entry => !entriesToRemove.includes(entry.id));
          setEntries(newEntries);
          
          toast.success(`Undo successful - Removed Row 5 and dependent entries`);
          return;
        }
        
        // NEW CASE: Undo for Row 4 should also undo Row 3
        if (lastEntry.rowNumber === 4) {
          console.log(`Extend-2-1-1 special case: Undoing Row 4 in Column ${lastEntry.columnNumber}`);
          
          // Find Row 3 entry as Row 4 is auto-populated from Row 3
          const row3Entry = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber === 3
          );
          
          const entriesToRemove = [lastEntry.id];
          
          if (row3Entry) {
            console.log(`Also removing Row 3 from Column ${lastEntry.columnNumber}`);
            entriesToRemove.push(row3Entry.id);
          }
          
          // Also remove any dependent rows (Row 5, Row 6, and Row 7)
          const dependentRows = entries.filter(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber > 4
          ).map(entry => entry.id);
          
          entriesToRemove.push(...dependentRows);
          
          // Also remove any entries in the next column that might have been auto-populated
          const nextColumnEntries = entries.filter(
            entry => entry.columnNumber === lastEntry.columnNumber + 1
          ).map(entry => entry.id);
          
          if (nextColumnEntries.length > 0) {
            console.log(`Also removing dependent entries from Column ${lastEntry.columnNumber + 1}`);
            entriesToRemove.push(...nextColumnEntries);
          }
          
          const newEntries = entries.filter(entry => !entriesToRemove.includes(entry.id));
          setEntries(newEntries);
          
          toast.success(`Undo successful - Removed Row 4 and dependent entries`);
          return;
        }
      } else {
        // Original logic for other extend modes
        if (lastEntry.rowNumber === 1 && lastEntry.columnNumber > 1) {
          console.log(`Special case: Undoing Row 1 in Column ${lastEntry.columnNumber}`);
          
          const prevColRow5Entry = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber - 1 && entry.rowNumber === 5
          );
          
          const entriesToRemove = [lastEntry.id];
          
          if (prevColRow5Entry) {
            console.log(`Also removing Row 5 from Column ${lastEntry.columnNumber - 1}`);
            entriesToRemove.push(prevColRow5Entry.id);
          }
          
          const dependentRows = entries.filter(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber > 1
          ).map(entry => entry.id);
          
          entriesToRemove.push(...dependentRows);
          
          const newEntries = entries.filter(entry => !entriesToRemove.includes(entry.id));
          setEntries(newEntries);
          
          toast.success(`Undo successful - Removed Column ${lastEntry.columnNumber} Row 1 and related entries`);
          return;
        }
        
        if (lastEntry.rowNumber === 4) {
          console.log(`Special case: Undoing pattern cell at Row 4 in Column ${lastEntry.columnNumber}`);
          
          // Find Row 3 entry as Row 4 is derived from Row 3
          const row3Entry = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber === 3
          );
          
          // Also need to remove any dependent Row 5 entries
          const dependentRows = entries.filter(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber > 4
          ).map(entry => entry.id);
          
          const entriesToRemove = [lastEntry.id];
          
          // Add Row 3 to entries to remove since Row 4 is derived from Row 3
          if (row3Entry) {
            console.log(`Also removing Row 3 (source for Row 4) from Column ${lastEntry.columnNumber}`);
            entriesToRemove.push(row3Entry.id);
          }
          
          // Add dependent rows
          entriesToRemove.push(...dependentRows);
          
          // Remove all dependent rows
          const dependentRowsAfterPatternCell = entries.filter(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber > lastEntry.rowNumber
          ).map(entry => entry.id);
          
          entriesToRemove.push(...dependentRowsAfterPatternCell);
          
          // Also remove any entries in the next column that might have been auto-populated
          const nextColumnEntries = entries.filter(
            entry => entry.columnNumber === lastEntry.columnNumber + 1
          ).map(entry => entry.id);
          
          if (nextColumnEntries.length > 0) {
            console.log(`Also removing dependent entries from Column ${lastEntry.columnNumber + 1}`);
            entriesToRemove.push(...nextColumnEntries);
          }
          
          const newEntries = entries.filter(entry => !entriesToRemove.includes(entry.id));
          setEntries(newEntries);
          
          toast.success(`Undo successful - Removed pattern cell, source row, and related entries`);
          return;
        }
        
        if (lastEntry.rowNumber === 3) {
          console.log(`Special case: Undoing Row 3 (source for pattern cell) in Column ${lastEntry.columnNumber}`);
          
          const row4PatternCell = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber === 4
          );
          
          const dependentRows = entries.filter(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber > 3
          ).map(entry => entry.id);
          
          const entriesToRemove = [lastEntry.id, ...dependentRows];
          
          if (row4PatternCell) {
            console.log(`Also removing dependent pattern cell at Row 4`);
            if (!entriesToRemove.includes(row4PatternCell.id)) {
              entriesToRemove.push(row4PatternCell.id);
            }
          }
          
          const newEntries = entries.filter(entry => !entriesToRemove.includes(entry.id));
          setEntries(newEntries);
          
          toast.success(`Undo successful - Removed Row 3 and dependent entries`);
          return;
        }
        
        if (lastEntry.rowNumber === 5) {
          console.log(`Special case: Undoing Row 5 in Column ${lastEntry.columnNumber}`);
          
          const nextColRow1Entry = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber + 1 && entry.rowNumber === 1
          );
          
          const entriesToRemove = [lastEntry.id];
          
          if (nextColRow1Entry) {
            console.log(`Also removing dependent Row 1 from Column ${lastEntry.columnNumber + 1}`);
            entriesToRemove.push(nextColRow1Entry.id);
            
            const nextColEntries = entries.filter(
              entry => entry.columnNumber === lastEntry.columnNumber + 1
            ).map(entry => entry.id);
            
            entriesToRemove.push(...nextColEntries);
          }
          
          const newEntries = entries.filter(entry => !entriesToRemove.includes(entry.id));
          setEntries(newEntries);
          
          toast.success(`Undo successful - Removed Row 5 and related entries`);
          return;
        }
      }
      
      // Special handling for Extend-2-1-1-Recovery logic
      if (predictionLogic === 'extend-2-1-1-recovery') {
        // If we're undoing Row 7, we need to also undo any Row 1 entries in the next column
        if (lastEntry.rowNumber === 7) {
          console.log(`Extend-2-1-1-Recovery special case: Undoing Row 7 in Column ${lastEntry.columnNumber}`);
          
          // Find any Row 1 entries in the next column
          const nextColumnRow1 = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber + 1 && entry.rowNumber === 1
          );
          
          // Also find any other entries in the next column
          const nextColumnEntries = entries.filter(
            entry => entry.columnNumber === lastEntry.columnNumber + 1
          ).map(entry => entry.id);
          
          const entriesToRemove = [lastEntry.id];
          
          if (nextColumnRow1) {
            entriesToRemove.push(nextColumnRow1.id);
            
            // Also remove any other entries in the next column
            entriesToRemove.push(...nextColumnEntries);
          }
          
          const newEntries = entries.filter(entry => !entriesToRemove.includes(entry.id));
          setEntries(newEntries);
          
          toast.success(`Undo successful - Removed Row 7 and related entries`);
          return;
        }
        
        // If we're undoing Row 6, we need to also undo Row 7
        if (lastEntry.rowNumber === 6) {
          console.log(`Extend-2-1-1-Recovery special case: Undoing Row 6 in Column ${lastEntry.columnNumber}`);
          
          // Find any Row 7 entry in the same column
          const row7Entry = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber === 7
          );
          
          // Also find any Row 1 entries in the next column
          const nextColumnRow1 = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber + 1 && entry.rowNumber === 1
          );
          
          // Also find any other entries in the next column
          const nextColumnEntries = entries.filter(
            entry => entry.columnNumber === lastEntry.columnNumber + 1
          ).map(entry => entry.id);
          
          const entriesToRemove = [lastEntry.id];
          
          if (row7Entry) {
            entriesToRemove.push(row7Entry.id);
          }
          
          if (nextColumnRow1) {
            entriesToRemove.push(nextColumnRow1.id);
            
            // Also remove any other entries in the next column
            entriesToRemove.push(...nextColumnEntries);
          }
          
          const newEntries = entries.filter(entry => !entriesToRemove.includes(entry.id));
          setEntries(newEntries);
          
          toast.success(`Undo successful - Removed Row 6 and related entries`);
          return;
        }
        
        // If we're undoing Row 1, we need to remove the entire column
        if (lastEntry.rowNumber === 1) {
          console.log(`Extend-2-1-1-Recovery special case: Undoing Row 1 in Column ${lastEntry.columnNumber}`);
          
          // Remove all entries in this column
          const columnEntries = entries.filter(
            entry => entry.columnNumber === lastEntry.columnNumber
          ).map(entry => entry.id);
          
          const newEntries = entries.filter(entry => !columnEntries.includes(entry.id));
          setEntries(newEntries);
          
          toast.success(`Undo successful - Removed entire column`);
          return;
        }
        
        // If we're undoing Row 5, we need to also undo Row 6 and Row 7
        if (lastEntry.rowNumber === 5) {
          console.log(`Extend-2-1-1-Recovery special case: Undoing Row 5 in Column ${lastEntry.columnNumber}`);
          
          // Find any Row 6 and Row 7 entries in the same column
          const row6Entry = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber === 6
          );
          
          const row7Entry = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber === 7
          );
          
          // Also find any Row 1 entries in the next column
          const nextColumnRow1 = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber + 1 && entry.rowNumber === 1
          );
          
          // Also find any other entries in the next column
          const nextColumnEntries = entries.filter(
            entry => entry.columnNumber === lastEntry.columnNumber + 1
          ).map(entry => entry.id);
          
          const entriesToRemove = [lastEntry.id];
          
          if (row6Entry) {
            entriesToRemove.push(row6Entry.id);
          }
          
          if (row7Entry) {
            entriesToRemove.push(row7Entry.id);
          }
          
          if (nextColumnRow1) {
            entriesToRemove.push(nextColumnRow1.id);
            
            // Also remove any other entries in the next column
            entriesToRemove.push(...nextColumnEntries);
          }
          
          const newEntries = entries.filter(entry => !entriesToRemove.includes(entry.id));
          setEntries(newEntries);
          
          toast.success(`Undo successful - Removed Row 5 and related entries`);
          return;
        }
        
        // If we're undoing Row 4, we need to also undo Row 5, Row 6, and Row 7
        if (lastEntry.rowNumber === 4) {
          console.log(`Extend-2-1-1-Recovery special case: Undoing Row 4 in Column ${lastEntry.columnNumber}`);
          
          // Find any Row 5, Row 6, and Row 7 entries in the same column
          const row5Entry = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber === 5
          );
          
          const row6Entry = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber === 6
          );
          
          const row7Entry = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber && entry.rowNumber === 7
          );
          
          // Also find any Row 1 entries in the next column
          const nextColumnRow1 = entries.find(
            entry => entry.columnNumber === lastEntry.columnNumber + 1 && entry.rowNumber === 1
          );
          
          // Also find any other entries in the next column
          const nextColumnEntries = entries.filter(
            entry => entry.columnNumber === lastEntry.columnNumber + 1
          ).map(entry => entry.id);
          
          const entriesToRemove = [lastEntry.id];
          
          if (row5Entry) {
            entriesToRemove.push(row5Entry.id);
          }
          
          if (row6Entry) {
            entriesToRemove.push(row6Entry.id);
          }
          
          if (row7Entry) {
            entriesToRemove.push(row7Entry.id);
          }
          
          if (nextColumnRow1) {
            entriesToRemove.push(nextColumnRow1.id);
            
            // Also remove any other entries in the next column
            entriesToRemove.push(...nextColumnEntries);
          }
          
          const newEntries = entries.filter(entry => !entriesToRemove.includes(entry.id));
          setEntries(newEntries);
          
          toast.success(`Undo successful - Removed Row 4 and related entries`);
          return;
        }
      }
    }
    
    const newEntries = entries.slice(0, -1);
    setEntries(newEntries);
    
    toast.success('Undo Successful');
  };

  const handleReset = () => {
    setEntries([]);
    setLastResetIndex(-1);
    setResetIndices([]);
    setIsGameReset(true);
    setCurrentResetColumn(null);
    
    toast.success("Game Reset - All game data has been cleared.");
  };

  const calculateChartData = () => {
    const chartData: { column: number; profitLoss: number; }[] = [];
    
    if (entries.length === 0) return chartData;
    
    const maxColumn = entries[entries.length - 1].columnNumber;
    
    for (let column = 1; column <= maxColumn; column++) {
      const columnEntries = entries.filter(entry => entry.columnNumber === column);
      
      if (columnEntries.length > 0) {
        const lastEntry = columnEntries[columnEntries.length - 1];
        chartData.push({
          column,
          profitLoss: lastEntry.cumulativeProfitLoss
        });
      }
    }
    
    return chartData;
  };

  return {
    baseBet,
    setBaseBet,
    raiseBet,
    setRaiseBet,
    rowCount,
    betStrategy,
    setBetStrategy,
    entries,
    setEntries,
    predictionLogic,
    setPredictionLogic,
    resetBettingHands,
    setResetBettingHands,
    resetIndices,
    lastResetIndex,
    isGameReset,
    setIsGameReset,
    extensionFactor,
    setExtensionFactor,
    handleRowCountChange,
    handleExtensionFactorChange,
    handleHandSelect: handleHandSelectForPosition,
    handleUndo,
    handleReset,
    calculateChartData
  };
};
