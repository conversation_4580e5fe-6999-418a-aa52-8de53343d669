import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Toggle } from "@/components/ui/toggle";
import { useGameGrid } from '@/hooks/useGameGrid';
import { ExtensionFactor, GameEntry, PredictionLogicType } from '@/types/game';
import { isPatternCell } from '@/utils/nextBetCalculation';
import { exportToPDF } from '@/utils/pdf';
import { Download, Grid, Table } from 'lucide-react';
import { useEffect, useState } from 'react';
import GameGrid from './GameGrid';
import SimpleGameGrid from './SimpleGameGrid';
import { Button } from './ui/button';

interface GameContainerProps {
  entries: GameEntry[];
  rowCount: number;
  onHandSelect: (columnNumber: number, rowNumber: number, value: 'P' | 'B') => void;
  predictionLogic?: PredictionLogicType;
  resetIndices?: number[];
  extensionFactor?: ExtensionFactor;
  onUndo?: () => void;
}

const GameContainer = ({ 
  entries, 
  rowCount, 
  onHandSelect,
  predictionLogic = 'same',
  resetIndices = [],
  extensionFactor = 2,
  onUndo
}: GameContainerProps) => {
  const [isSimpleView, setIsSimpleView] = useState(false);
  const isWinLossMode = predictionLogic === 'win-loss' || predictionLogic === 'win-loss-prev-rows';
  const isExtendMode = predictionLogic === 'extend' || predictionLogic === 'extend-reset' || predictionLogic === 'extend-pattern-change' || predictionLogic === 'extend-2-1-1' || predictionLogic === 'extend-2-1-1-recovery';
  const baseRowCount = isExtendMode ? 3 : rowCount; // Always use 3 as base for extend modes
  const { getPatternSourceRow } = useGameGrid(entries, rowCount, predictionLogic, extensionFactor, baseRowCount);
  
  const getGridDimensions = () => {
    if (!isExtendMode) {
      return {
        effectiveRowCount: rowCount,
        columnCount: rowCount === 3 ? 52 : rowCount === 4 ? 52 : rowCount === 6 ? 52 : 52
      };
    }
    
    // For extend-2-1-1 and extend-2-1-1-recovery logic, use 7 rows
    if (predictionLogic === 'extend-2-1-1' || predictionLogic === 'extend-2-1-1-recovery') {
      const effectiveRowCount = 7;
      const columnCount = 21;
      
      console.log(`Using dimensions for ${predictionLogic}: ${effectiveRowCount} rows x ${columnCount} columns`);
      
      return { effectiveRowCount, columnCount };
    }
    
    // For all other extend-based logics, use the same dimensions
    // Always use exactly 5 rows for other extend-based modes
    const effectiveRowCount = 5;
    const columnCount = 21;
    
    console.log(`Using dimensions for ${predictionLogic}: ${effectiveRowCount} rows x ${columnCount} columns`);
    
    return { effectiveRowCount, columnCount };
  };
  
  const { effectiveRowCount, columnCount } = getGridDimensions();
  const hasEntries = entries.length > 0;

  useEffect(() => {
    if (isExtendMode && entries.length > 0) {
      const columns = [...new Set(entries.map(entry => entry.columnNumber))];
      
      // Auto-populate pattern cells (No Bet cells)
      const patternPositions = [baseRowCount + 1]; // Row 4 for 3-row base grid
      
      // Handle column transitions for all columns
      for (const column of columns) {
        // Get the next column number
        const nextColumn = column + 1;
        
        // Check if this column has a complete set of rows or at least has Row 5/7 (depending on prediction logic)
        const lastRowOfCurrentColumn = predictionLogic === 'extend-2-1-1' || predictionLogic === 'extend-2-1-1-recovery' ? 7 : 5;
        
        const hasLastRow = entries.some(
          entry => entry.columnNumber === column && 
                  entry.rowNumber === lastRowOfCurrentColumn
        );
        
        // If current column has the last row filled (Row 5 for regular extend, Row 7 for extend-2-1-1)
        if (hasLastRow) {
          // Check if next column's Row 1 exists
          const nextColumnRow1Exists = entries.some(
            entry => entry.columnNumber === nextColumn && entry.rowNumber === 1
          );
          
          // If next column's Row 1 doesn't exist yet, populate it with the value from the last row
          if (!nextColumnRow1Exists) {
            // For extend-2-1-1, we need to use Row 7's value
            const lastRowToUse = predictionLogic === 'extend-2-1-1' || predictionLogic === 'extend-2-1-1-recovery' ? 7 : lastRowOfCurrentColumn;
            
            const lastRowEntry = entries.find(
              entry => entry.columnNumber === column && 
                      entry.rowNumber === lastRowToUse
            );
            
            if (lastRowEntry?.handValue) {
              console.log(`Auto-populating next column ${nextColumn} Row 1 with value ${lastRowEntry.handValue} from column ${column} row ${lastRowToUse}`);
              onHandSelect(nextColumn, 1, lastRowEntry.handValue);
              
              // Exit after populating one row to avoid cascading effects
              return;
            }
          }
        }
      }
      
      // Then handle pattern cell population
      for (const column of columns) {
        const columnHasEntriesBeyondFirstRow = entries.some(
          entry => entry.columnNumber === column && entry.rowNumber > 1
        );
        
        if (!columnHasEntriesBeyondFirstRow) {
          continue;
        }
        
        for (const patternRow of patternPositions) {
          const existingPatternCell = entries.find(
            entry => entry.columnNumber === column && entry.rowNumber === patternRow
          );
          
          if (!existingPatternCell) {
            // Get the source row for this pattern cell
            const sourceRow = getPatternSourceRow(patternRow);
            
            const sourceEntry = entries.find(
              entry => entry.columnNumber === column && entry.rowNumber === sourceRow
            );
            
            // Make sure we have a source value AND all rows between
            // the first row and the source row have entries
            if (sourceEntry?.handValue) {
              // Check that all rows from 1 to sourceRow have entries for this column
              const hasAllPrecedingRows = Array.from({ length: sourceRow }, (_, i) => i + 1)
                .every(row => entries.some(
                  entry => entry.columnNumber === column && entry.rowNumber === row
                ));
              
              if (hasAllPrecedingRows) {
                // For extend-pattern-change mode, Row 4 should use the OPPOSITE value of Row 3
                let valueToUse = sourceEntry.handValue;
                
                if (predictionLogic === 'extend-pattern-change' && patternRow === 4) {
                  valueToUse = valueToUse === 'P' ? 'B' : 'P';
                  console.log(`Auto-populating pattern cell at column ${column}, row ${patternRow} with OPPOSITE value ${valueToUse} from row ${sourceRow}`);
                } else {
                  console.log(`Auto-populating pattern cell at column ${column}, row ${patternRow} with value ${valueToUse} from row ${sourceRow}`);
                }
                
                onHandSelect(column, patternRow, valueToUse);
              }
            }
          }
        }
      }
    }
  }, [entries, predictionLogic, baseRowCount, onHandSelect, getPatternSourceRow, extensionFactor, isExtendMode]);

  const handleHandSelect = (columnNumber: number, rowNumber: number, value: 'P' | 'B') => {
    if (isExtendMode) {
      console.log('GameContainer.handleHandSelect:', { columnNumber, rowNumber, value });
      
      const isPatternCellRow = isPatternCell(rowNumber, predictionLogic, extensionFactor, rowCount);
      
      if (isPatternCellRow) {
        console.log(`${predictionLogic} mode: Updating pattern cell at column ${columnNumber}, row ${rowNumber} with value ${value}`);
        onHandSelect(columnNumber, rowNumber, value);
        return;
      }
    }
    
    // Always pass the columnNumber, rowNumber and value directly to onHandSelect
    // This ensures Row 1 values are manually entered
    onHandSelect(columnNumber, rowNumber, value);
  };

  useEffect(() => {
    if (entries.length > 0) {
      const hasEntries = entries.length > 0;
      if (hasEntries) {
        setTimeout(() => {
          const lastEntry = entries[entries.length - 1];
          if (lastEntry) {
            const element = document.getElementById(`cell-${lastEntry.columnNumber}-${lastEntry.rowNumber}`);
            if (element) {
              element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
          }
        }, 100);
      }
    }
  }, [entries, entries.length, hasEntries]);

  return (
    <Card className="border-0 shadow-none">
      <CardHeader className="px-0 pt-0 flex-row justify-between items-center flex-wrap gap-2">
        <CardTitle className="text-base font-semibold text-primary">Game Progress</CardTitle>
        <div className="flex items-center gap-2">
          <Toggle
            pressed={isSimpleView}
            onPressedChange={setIsSimpleView}
            aria-label="Toggle grid view"
            className="flex items-center gap-1 text-xs sm:text-sm h-8"
          >
            {isSimpleView ? <Grid className="h-3 w-3 md:h-4 md:w-4" /> : <Table className="h-3 w-3 md:h-4 md:w-4" />}
            {isSimpleView ? 'Simple' : 'Detailed'}
          </Toggle>
          <Button
            onClick={() => exportToPDF(entries, !isSimpleView, predictionLogic, extensionFactor)}
            className="flex items-center gap-1 text-xs h-8"
            variant="outline"
            disabled={!hasEntries}
            size="sm"
          >
            <Download className="w-3 h-3" />
            Export
          </Button>
        </div>
      </CardHeader>
      <CardContent className="px-0 pb-0">
        <div id="game-grid-container" className="w-full rounded-lg">
          {isSimpleView ? (
            <SimpleGameGrid 
              entries={entries} 
              rowCount={effectiveRowCount} 
              columnCount={columnCount} 
              onHandSelect={handleHandSelect} 
              resetIndices={resetIndices}
              predictionLogic={predictionLogic}
              extensionFactor={extensionFactor}
              baseRowCount={rowCount}
            />
          ) : (
            <GameGrid 
              entries={entries} 
              rowCount={effectiveRowCount} 
              columnCount={columnCount} 
              onHandSelect={handleHandSelect}
              resetIndices={resetIndices}
              predictionLogic={predictionLogic}
              extensionFactor={extensionFactor}
              baseRowCount={rowCount}
            />
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default GameContainer;
