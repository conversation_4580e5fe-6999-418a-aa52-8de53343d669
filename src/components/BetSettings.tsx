import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { GameEntry, PredictionLogicOption, PredictionLogicType } from '@/types/game';

interface BetSettingsProps {
  baseBet: number;
  raiseBet: number;
  rowCount: number;
  betStrategy: 'loss' | 'win';
  onBaseBetChange: (value: number) => void;
  onRaiseBetChange: (value: number) => void;
  onRowCountChange: (value: number) => void;
  onBetStrategyChange: (value: 'loss' | 'win') => void;
  entries: GameEntry[];
  predictionLogic: PredictionLogicType;
  onPredictionLogicChange: (value: PredictionLogicType) => void;
  resetBettingHands: number;
  onResetBettingHandsChange: (value: number) => void;
  extensionFactor?: 2 | 3;
  onExtensionFactorChange?: (value: 2 | 3) => void;
  layout?: 'horizontal' | 'vertical';
  predictionLogicOptions: PredictionLogicOption[];
  hideBetStrategy?: boolean;
  hideResetBettingHands?: boolean;
  hideRowCount?: boolean;
}

const BetSettings = ({
  baseBet,
  raiseBet,
  rowCount,
  betStrategy,
  onBaseBetChange,
  onRaiseBetChange,
  onRowCountChange,
  onBetStrategyChange,
  entries,
  predictionLogic,
  onPredictionLogicChange,
  resetBettingHands,
  onResetBettingHandsChange,
  extensionFactor = 2,
  onExtensionFactorChange = () => {},
  layout = 'horizontal',
  predictionLogicOptions,
  hideBetStrategy = false,
  hideResetBettingHands = false,
  hideRowCount = false
}: BetSettingsProps) => {
  
  // Check if game has started (entries exist)
  const gameHasStarted = entries.length > 0;
  
  // Handle numeric input changes with empty value support
  const handleNumericChange = (value: string, onChange: (val: number) => void) => {
    // Allow empty string (will be converted to 0 later when needed)
    if (value === '') {
      onChange(0);
      return;
    }
    
    // Only update if it's a valid number
    const numValue = parseInt(value);
    if (!isNaN(numValue)) {
      onChange(numValue);
    }
  };
  
  // Handle prediction logic change
  const handlePredictionLogicChange = (value: PredictionLogicType) => {
    if (value === 'extend' || value === 'extend-reset' || value === 'extend-pattern-change') {
      // Set default values for extend-based logics
      onRowCountChange(3); // Always use 3 rows for extend logic
      if (onExtensionFactorChange) {
        onExtensionFactorChange(2); // Default to 2 for extension factor
      }
    }
    onPredictionLogicChange(value);
  };
  
  return (
    <div className={`grid gap-4 ${layout === 'horizontal' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'grid-cols-1'}`}>
      <div>
        <Label htmlFor="predictionLogic" className="text-sm font-medium">
          Prediction Logic
        </Label>
        <Select 
          value={predictionLogic} 
          onValueChange={(value: PredictionLogicType) => handlePredictionLogicChange(value)}
          disabled={gameHasStarted}
        >
          <SelectTrigger id="predictionLogic" className="w-full">
            <SelectValue placeholder="Select prediction logic" />
          </SelectTrigger>
          <SelectContent>
            {predictionLogicOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="baseBet" className="text-sm font-medium">
          Base Bet
        </Label>
        <Input
          id="baseBet"
          type="text"
          inputMode="numeric"
          value={baseBet === 0 ? '' : baseBet.toString()}
          onChange={(e) => handleNumericChange(e.target.value, onBaseBetChange)}
          className="w-full"
          disabled={gameHasStarted}
        />
      </div>

      <div>
        <Label htmlFor="raiseBet" className="text-sm font-medium">
          Raise Bet Amount
        </Label>
        <Input
          id="raiseBet"
          type="text"
          inputMode="numeric"
          value={raiseBet === 0 ? '' : raiseBet.toString()}
          onChange={(e) => handleNumericChange(e.target.value, onRaiseBetChange)}
          className="w-full"
          disabled={gameHasStarted}
        />
      </div>

      {!hideRowCount && (
        <div>
          <Label htmlFor="rowCount" className="text-sm font-medium">
            Grid Layout
          </Label>
          <Select 
            value={rowCount.toString()} 
            onValueChange={(value) => onRowCountChange(parseInt(value))}
            disabled={gameHasStarted}
          >
            <SelectTrigger id="rowCount" className="w-full">
              <SelectValue placeholder="Select row count" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="3">3 Rows × 52 Columns</SelectItem>
              <SelectItem value="4">4 Rows × 52 Columns</SelectItem>
              <SelectItem value="6">6 Rows × 52 Columns</SelectItem>
              <SelectItem value="8">8 Rows × 52 Columns</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )}

      {!hideBetStrategy && (
        <div>
          <Label htmlFor="betStrategy" className="text-sm font-medium">
            Bet Strategy
          </Label>
          <Select 
            value={betStrategy} 
            onValueChange={(value: 'loss' | 'win') => onBetStrategyChange(value)}
            disabled={gameHasStarted}
          >
            <SelectTrigger id="betStrategy" className="w-full">
              <SelectValue placeholder="Select bet strategy" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="loss">Loss</SelectItem>
              <SelectItem value="win">Win</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )}

      {!hideResetBettingHands && (
        <div>
          <Label htmlFor="resetBettingHands" className="text-sm font-medium">
            Reset After Betting Hands
          </Label>
          <Input
            id="resetBettingHands"
            type="text"
            inputMode="numeric"
            value={resetBettingHands === 0 ? '' : resetBettingHands.toString()}
            onChange={(e) => handleNumericChange(e.target.value, onResetBettingHandsChange)}
            className="w-full"
            disabled={gameHasStarted}
          />
        </div>
      )}
    </div>
  );
};

export default BetSettings;
